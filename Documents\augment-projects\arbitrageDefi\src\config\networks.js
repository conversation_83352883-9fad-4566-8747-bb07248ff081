require('dotenv').config();

const networks = {
  ethereum: {
    name: 'Ethereum',
    chainId: 1,
    rpcUrl: process.env.MAINNET_RPC_URL,
    nativeCurrency: 'ETH',
    blockExplorer: 'https://etherscan.io',
    avgGasPrice: 30, // gwei
    avgBlockTime: 12, // seconds
    priority: 4, // Lower priority due to high fees
    minTradeSize: 50 // USD - high due to gas costs
  },
  arbitrum: {
    name: 'Arbitrum One',
    chainId: 42161,
    rpcUrl: process.env.ARBITRUM_RPC_URL,
    nativeCurrency: 'ETH',
    blockExplorer: 'https://arbiscan.io',
    avgGasPrice: 0.1, // gwei
    avgBlockTime: 1, // seconds
    priority: 1, // High priority - low fees
    minTradeSize: 5 // USD
  },
  optimism: {
    name: 'Optimism',
    chainId: 10,
    rpcUrl: process.env.OPTIMISM_RPC_URL,
    nativeCurrency: 'ETH',
    blockExplorer: 'https://optimistic.etherscan.io',
    avgGasPrice: 0.001, // gwei
    avgBlockTime: 2, // seconds
    priority: 2, // High priority - low fees
    minTradeSize: 5 // USD
  },
  base: {
    name: 'Base',
    chainId: 8453,
    rpcUrl: process.env.BASE_RPC_URL,
    nativeCurrency: 'ETH',
    blockExplorer: 'https://basescan.org',
    avgGasPrice: 0.001, // gwei
    avgBlockTime: 2, // seconds
    priority: 2, // High priority - low fees
    minTradeSize: 5 // USD
  },
  polygon: {
    name: 'Polygon',
    chainId: 137,
    rpcUrl: process.env.POLYGON_RPC_URL,
    nativeCurrency: 'MATIC',
    blockExplorer: 'https://polygonscan.com',
    avgGasPrice: 30, // gwei (but MATIC is cheap)
    avgBlockTime: 2, // seconds
    priority: 1, // High priority - very low fees
    minTradeSize: 2 // USD
  }
};

// Get networks sorted by priority (lower number = higher priority)
const getNetworksByPriority = () => {
  return Object.entries(networks)
    .sort(([,a], [,b]) => a.priority - b.priority)
    .map(([key, network]) => ({ key, ...network }));
};

// Get networks suitable for small capital
const getSmallCapitalNetworks = (maxCapital = 13.70) => {
  return Object.entries(networks)
    .filter(([, network]) => network.minTradeSize <= maxCapital * 0.5)
    .sort(([,a], [,b]) => a.priority - b.priority)
    .map(([key, network]) => ({ key, ...network }));
};

module.exports = {
  networks,
  getNetworksByPriority,
  getSmallCapitalNetworks
};
