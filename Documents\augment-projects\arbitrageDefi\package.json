{"name": "defi-arbitrage-detector", "version": "1.0.0", "description": "DeFi arbitrage opportunity detection system for small capital accounts", "main": "src/main.js", "scripts": {"start": "node src/main.js", "dev": "nodemon src/main.js", "dashboard": "node src/dashboard/server.js", "test": "jest"}, "keywords": ["defi", "arbitrage", "cryptocurrency", "trading", "dex"], "author": "DeFi Arbitrage System", "license": "MIT", "dependencies": {"ethers": "^6.8.1", "express": "^4.18.2", "ws": "^8.14.2", "axios": "^1.6.0", "dotenv": "^16.3.1", "node-cron": "^3.0.3", "chalk": "^4.1.2", "cors": "^2.8.5", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}, "engines": {"node": ">=16.0.0"}}