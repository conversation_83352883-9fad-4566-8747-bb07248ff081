const { ethers } = require('ethers');
const axios = require('axios');
const { networks } = require('../config/networks');

class GasEstimator {
  constructor() {
    this.providers = {};
    this.gasCache = new Map();
    this.cacheTimeout = 30000; // 30 seconds
    
    // Initialize providers for each network
    Object.entries(networks).forEach(([key, network]) => {
      this.providers[key] = new ethers.JsonRpcProvider(network.rpcUrl);
    });
  }

  // Get current gas price for a network
  async getGasPrice(networkKey) {
    const cacheKey = `gasPrice_${networkKey}`;
    const cached = this.gasCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    try {
      const provider = this.providers[networkKey];
      if (!provider) {
        throw new Error(`No provider for network: ${networkKey}`);
      }

      let gasPrice;
      
      // For Ethereum mainnet, use more sophisticated gas estimation
      if (networkKey === 'ethereum') {
        gasPrice = await this.getEthereumGasPrice();
      } else {
        // For L2s, use simple gas price
        const feeData = await provider.getFeeData();
        gasPrice = feeData.gasPrice;
      }

      const result = {
        gasPrice: gasPrice.toString(),
        gasPriceGwei: ethers.formatUnits(gasPrice, 'gwei'),
        timestamp: Date.now(),
        networkKey
      };

      this.gasCache.set(cacheKey, { data: result, timestamp: Date.now() });
      return result;

    } catch (error) {
      console.error(`Error getting gas price for ${networkKey}:`, error.message);
      
      // Return fallback gas price from config
      const network = networks[networkKey];
      const fallbackGasPrice = ethers.parseUnits(network.avgGasPrice.toString(), 'gwei');
      
      return {
        gasPrice: fallbackGasPrice.toString(),
        gasPriceGwei: network.avgGasPrice.toString(),
        timestamp: Date.now(),
        networkKey,
        fallback: true
      };
    }
  }

  // Enhanced gas price estimation for Ethereum mainnet
  async getEthereumGasPrice() {
    try {
      // Try to get gas price from Etherscan API
      const response = await axios.get(
        `https://api.etherscan.io/api?module=gastracker&action=gasoracle&apikey=${process.env.ETHERSCAN_API_KEY}`,
        { timeout: 5000 }
      );

      if (response.data.status === '1') {
        // Use "fast" gas price for arbitrage (we want quick execution)
        const fastGasPrice = response.data.result.FastGasPrice;
        return ethers.parseUnits(fastGasPrice, 'gwei');
      }
    } catch (error) {
      console.warn('Etherscan gas API failed, using provider fallback');
    }

    // Fallback to provider
    const provider = this.providers.ethereum;
    const feeData = await provider.getFeeData();
    return feeData.gasPrice;
  }

  // Estimate gas cost for a swap transaction
  async estimateSwapGasCost(networkKey, tokenIn, tokenOut, amountIn) {
    try {
      const gasData = await this.getGasPrice(networkKey);
      const network = networks[networkKey];
      
      // Estimated gas units for different transaction types
      let estimatedGasUnits;
      
      switch (networkKey) {
        case 'ethereum':
          // Ethereum mainnet - higher gas usage
          estimatedGasUnits = 150000; // Conservative estimate for Uniswap V3 swap
          break;
        case 'arbitrum':
        case 'optimism':
        case 'base':
          // L2 networks - lower gas usage
          estimatedGasUnits = 100000;
          break;
        case 'polygon':
          // Polygon - moderate gas usage
          estimatedGasUnits = 120000;
          break;
        default:
          estimatedGasUnits = 120000;
      }

      const gasCostWei = BigInt(gasData.gasPrice) * BigInt(estimatedGasUnits);
      const gasCostEth = ethers.formatEther(gasCostWei);
      
      // Convert to USD (rough estimate - in production, you'd want real-time ETH price)
      let ethPriceUSD = 2000; // Fallback ETH price
      if (networkKey === 'polygon') {
        ethPriceUSD = 0.8; // MATIC price estimate
      }
      
      const gasCostUSD = parseFloat(gasCostEth) * ethPriceUSD;

      return {
        gasUnits: estimatedGasUnits,
        gasPriceWei: gasData.gasPrice,
        gasPriceGwei: gasData.gasPriceGwei,
        gasCostWei: gasCostWei.toString(),
        gasCostEth: gasCostEth,
        gasCostUSD: gasCostUSD,
        networkKey,
        timestamp: Date.now()
      };

    } catch (error) {
      console.error(`Error estimating gas cost for ${networkKey}:`, error.message);
      
      // Return conservative fallback estimate
      return {
        gasUnits: 150000,
        gasCostUSD: networkKey === 'ethereum' ? 15 : 1, // Conservative estimates
        networkKey,
        timestamp: Date.now(),
        fallback: true
      };
    }
  }

  // Calculate total transaction costs including slippage
  async calculateTotalTransactionCost(networkKey, tradeAmount, slippagePercent = 0.5) {
    try {
      const gasCost = await this.estimateSwapGasCost(networkKey);
      
      // Calculate slippage cost
      const slippageCost = (tradeAmount * slippagePercent) / 100;
      
      // Get DEX trading fee (assume 0.3% average)
      const tradingFee = tradeAmount * 0.003;
      
      const totalCost = gasCost.gasCostUSD + slippageCost + tradingFee;
      
      return {
        gasCostUSD: gasCost.gasCostUSD,
        slippageCostUSD: slippageCost,
        tradingFeeUSD: tradingFee,
        totalCostUSD: totalCost,
        networkKey,
        tradeAmount,
        breakdown: {
          gas: gasCost.gasCostUSD,
          slippage: slippageCost,
          tradingFee: tradingFee
        }
      };

    } catch (error) {
      console.error(`Error calculating total transaction cost:`, error.message);
      throw error;
    }
  }

  // Check if a trade is profitable after all costs
  isProfitable(priceDifference, totalCosts, minProfitUSD = 2) {
    const netProfit = priceDifference - totalCosts;
    return {
      isProfitable: netProfit >= minProfitUSD,
      netProfit,
      profitMargin: (netProfit / priceDifference) * 100,
      totalCosts,
      priceDifference
    };
  }
}

module.exports = GasEstimator;
