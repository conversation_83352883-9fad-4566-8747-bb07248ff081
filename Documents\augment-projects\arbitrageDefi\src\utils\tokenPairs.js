// Common token addresses across different networks
const tokenAddresses = {
  ethereum: {
    WETH: '******************************************',
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************',
    WBTC: '******************************************'
  },
  arbitrum: {
    WETH: '******************************************',
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************',
    WBTC: '******************************************'
  },
  optimism: {
    WETH: '******************************************',
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************',
    WBTC: '******************************************'
  },
  base: {
    WETH: '******************************************',
    USDC: '******************************************',
    DAI: '******************************************'
  },
  polygon: {
    WETH: '******************************************',
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************',
    WBTC: '******************************************',
    WMATIC: '******************************************'
  }
};

// High-volume, liquid trading pairs suitable for arbitrage
const tradingPairs = [
  // Stablecoin pairs (low volatility, good for small arbitrage)
  { token0: 'USDC', token1: 'USDT', category: 'stablecoin', priority: 1 },
  { token0: 'USDC', token1: 'DAI', category: 'stablecoin', priority: 1 },
  { token0: 'USDT', token1: 'DAI', category: 'stablecoin', priority: 1 },
  
  // ETH pairs (high volume)
  { token0: 'WETH', token1: 'USDC', category: 'major', priority: 2 },
  { token0: 'WETH', token1: 'USDT', category: 'major', priority: 2 },
  { token0: 'WETH', token1: 'DAI', category: 'major', priority: 2 },
  
  // BTC pairs
  { token0: 'WBTC', token1: 'WETH', category: 'major', priority: 3 },
  { token0: 'WBTC', token1: 'USDC', category: 'major', priority: 3 },
  
  // Network-specific pairs
  { token0: 'WMATIC', token1: 'USDC', category: 'native', priority: 2, networks: ['polygon'] },
  { token0: 'WMATIC', token1: 'WETH', category: 'native', priority: 2, networks: ['polygon'] }
];

// Get token address for specific network
const getTokenAddress = (networkKey, tokenSymbol) => {
  return tokenAddresses[networkKey]?.[tokenSymbol];
};

// Get all available tokens for a network
const getNetworkTokens = (networkKey) => {
  return tokenAddresses[networkKey] || {};
};

// Get trading pairs for a specific network
const getTradingPairsForNetwork = (networkKey) => {
  return tradingPairs.filter(pair => {
    // If pair has specific networks, check if current network is included
    if (pair.networks && !pair.networks.includes(networkKey)) {
      return false;
    }
    
    // Check if both tokens exist on this network
    const token0Address = getTokenAddress(networkKey, pair.token0);
    const token1Address = getTokenAddress(networkKey, pair.token1);
    
    return token0Address && token1Address;
  }).map(pair => ({
    ...pair,
    token0Address: getTokenAddress(networkKey, pair.token0),
    token1Address: getTokenAddress(networkKey, pair.token1),
    networkKey
  }));
};

// Get high-priority pairs (best for small capital)
const getHighPriorityPairs = (networkKey) => {
  return getTradingPairsForNetwork(networkKey)
    .filter(pair => pair.priority <= 2)
    .sort((a, b) => a.priority - b.priority);
};

// Get stablecoin pairs (lowest risk)
const getStablecoinPairs = (networkKey) => {
  return getTradingPairsForNetwork(networkKey)
    .filter(pair => pair.category === 'stablecoin');
};

module.exports = {
  tokenAddresses,
  tradingPairs,
  getTokenAddress,
  getNetworkTokens,
  getTradingPairsForNetwork,
  getHighPriorityPairs,
  getStablecoinPairs
};
