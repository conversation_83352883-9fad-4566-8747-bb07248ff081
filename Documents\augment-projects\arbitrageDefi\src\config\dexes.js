// DEX configurations for different networks
const dexConfigs = {
  // Ethereum Mainnet DEXes
  ethereum: {
    uniswapV3: {
      name: 'Uniswap V3',
      factory: '******************************************',
      router: '******************************************',
      fee: 0.003, // 0.3%
      subgraphUrl: 'https://api.thegraph.com/subgraphs/name/uniswap/uniswap-v3',
      type: 'v3'
    },
    uniswapV2: {
      name: 'Uniswap V2',
      factory: '******************************************',
      router: '******************************************',
      fee: 0.003, // 0.3%
      subgraphUrl: 'https://api.thegraph.com/subgraphs/name/uniswap/uniswap-v2',
      type: 'v2'
    },
    sushiswap: {
      name: 'Sushi<PERSON>wa<PERSON>',
      factory: '******************************************',
      router: '******************************************',
      fee: 0.003, // 0.3%
      subgraphUrl: 'https://api.thegraph.com/subgraphs/name/sushiswap/exchange',
      type: 'v2'
    }
  },
  
  // Arbitrum DEXes
  arbitrum: {
    uniswapV3: {
      name: 'Uniswap V3',
      factory: '******************************************',
      router: '******************************************',
      fee: 0.003,
      subgraphUrl: 'https://api.thegraph.com/subgraphs/name/ianlapham/arbitrum-minimal',
      type: 'v3'
    },
    sushiswap: {
      name: 'SushiSwap',
      factory: '0xc35DADB65012eC5796536bD9864eD8773aBc74C4',
      router: '0x1b02dA8Cb0d097eB8D57A175b88c7D8b47997506',
      fee: 0.003,
      subgraphUrl: 'https://api.thegraph.com/subgraphs/name/sushiswap/arbitrum-exchange',
      type: 'v2'
    },
    camelot: {
      name: 'Camelot',
      factory: '0x6EcCab422D763aC031210895C81787E87B91425',
      router: '0xc873fEcbd354f5A56E00E710B90EF4201db2448d',
      fee: 0.003,
      type: 'v2'
    }
  },
  
  // Optimism DEXes
  optimism: {
    uniswapV3: {
      name: 'Uniswap V3',
      factory: '******************************************',
      router: '******************************************',
      fee: 0.003,
      subgraphUrl: 'https://api.thegraph.com/subgraphs/name/ianlapham/optimism-post-regenesis',
      type: 'v3'
    },
    velodrome: {
      name: 'Velodrome',
      factory: '0x25CbdDb98b35ab1FF77413456B31EC81A6B6B746',
      router: '0x9c12939390052919aF3155f41Bf4160Fd3666A6e',
      fee: 0.002, // 0.2%
      type: 'v2'
    }
  },
  
  // Base DEXes
  base: {
    uniswapV3: {
      name: 'Uniswap V3',
      factory: '0x33128a8fC17869897dcE68Ed026d694621f6FDfD',
      router: '0x2626664c2603336E57B271c5C0b26F421741e481',
      fee: 0.003,
      type: 'v3'
    },
    aerodrome: {
      name: 'Aerodrome',
      factory: '0x420DD381b31aEf6683db6B902084cB0FFECe40Da',
      router: '0xcF77a3Ba9A5CA399B7c97c74d54e5b1Beb874E43',
      fee: 0.002,
      type: 'v2'
    }
  },
  
  // Polygon DEXes
  polygon: {
    uniswapV3: {
      name: 'Uniswap V3',
      factory: '******************************************',
      router: '******************************************',
      fee: 0.003,
      subgraphUrl: 'https://api.thegraph.com/subgraphs/name/ianlapham/uniswap-v3-polygon',
      type: 'v3'
    },
    sushiswap: {
      name: 'SushiSwap',
      factory: '0xc35DADB65012eC5796536bD9864eD8773aBc74C4',
      router: '0x1b02dA8Cb0d097eB8D57A175b88c7D8b47997506',
      fee: 0.003,
      subgraphUrl: 'https://api.thegraph.com/subgraphs/name/sushiswap/matic-exchange',
      type: 'v2'
    },
    quickswap: {
      name: 'QuickSwap',
      factory: '0x5757371414417b8C6CAad45bAeF941aBc7d3Ab32',
      router: '0xa5E0829CaCEd8fFDD4De3c43696c57F7D7A678ff',
      fee: 0.003,
      type: 'v2'
    }
  }
};

// Get all DEXes for a specific network
const getDexesForNetwork = (networkKey) => {
  return dexConfigs[networkKey] || {};
};

// Get all DEXes across all networks
const getAllDexes = () => {
  const allDexes = [];
  Object.entries(dexConfigs).forEach(([networkKey, dexes]) => {
    Object.entries(dexes).forEach(([dexKey, dexConfig]) => {
      allDexes.push({
        networkKey,
        dexKey,
        ...dexConfig
      });
    });
  });
  return allDexes;
};

// Get DEXes suitable for small capital (lower fees preferred)
const getLowFeeDexes = () => {
  return getAllDexes().filter(dex => dex.fee <= 0.003).sort((a, b) => a.fee - b.fee);
};

module.exports = {
  dexConfigs,
  getDexesForNetwork,
  getAllDexes,
  getLowFeeDexes
};
